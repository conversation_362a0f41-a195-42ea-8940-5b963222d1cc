<?php

namespace App\Http\Controllers\Medical;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Webkul\Product\Models\Product;
use App\Http\Controllers\Medical\ProductController;
use App\Models\ProductQuestion;

class ProductDetailController extends Controller
{
    protected $productController;

    public function __construct(ProductController $productController)
    {
        $this->productController = $productController;
    }

    /*get product detail*/
    public function index(Request $request) {
        $productId = $request->query('productId');
        $product = Product::with(['attribute_values.attribute', 'images', 'categories.translation', 'variants.attribute_values'])
                          ->find($productId);

        if (!$product) {
            abort(404);
        }

        // Xử lý dữ liệu sản phẩm
        $products = collect([$product]);
        $products = $this->productController->processProductData($products);
        $product = $products->first();

        // Lấy thông tin attributes động
        $productAttributes = $this->getProductAttributes($product);

        // L<PERSON>y sản phẩm cùng thương hiệu
        $relatedProducts = $this->getRelatedProductsByBrand($product);

        // Lấy câu hỏi của sản phẩm
        $productQuestions = ProductQuestion::with(['customer', 'answer.admin'])
            ->where('product_id', $productId)
            ->approved()
            ->newest()
            ->get();

        return view('medical::product_detail.product_detail', compact('product', 'productAttributes', 'relatedProducts', 'productQuestions'));
    }

    /**
     * Lấy thông tin attributes của sản phẩm
     */
    private function getProductAttributes($product)
    {
        $attributeCodes = [
            'brand',
            'manufacturer',
            'registration_number',
            'specification',
            'active_ingredient',
            'dosage_form',
            'indication',
            'description',
            'ingredients',
            'usage_instructions',
            'precautions',
            'manufacturing_info',
            'unit_type'
        ];

        $attributes = [];

        foreach ($attributeCodes as $code) {
            $attribute = $product->attribute_values->where('attribute.code', $code)->first();
            if ($attribute) {
                $value = $this->getAttributeValue($attribute);
                if ($value) {
                    $attributes[$code] = [
                        'label' => $attribute->attribute->admin_name,
                        'value' => $value
                    ];
                }
            }
        }

        // Lấy thông tin brand từ attribute options nếu là select
        if (isset($attributes['brand'])) {
            $brandAttribute = $product->attribute_values->where('attribute.code', 'brand')->first();
            if ($brandAttribute && $brandAttribute->attribute->type === 'select') {
                $brandOption = \DB::table('attribute_options')
                    ->where('id', $brandAttribute->integer_value)
                    ->first();
                if ($brandOption) {
                    $attributes['brand']['value'] = $brandOption->admin_name;
                    $attributes['brand']['option_id'] = $brandOption->id;
                }
            }
        }

        // Lấy thông tin active ingredient từ attribute options nếu là select
        if (isset($attributes['active_ingredient'])) {
            $activeIngredientAttribute = $product->attribute_values->where('attribute.code', 'active_ingredient')->first();
            if ($activeIngredientAttribute && $activeIngredientAttribute->attribute->type === 'select') {
                $activeIngredientOption = \DB::table('attribute_options')
                    ->where('id', $activeIngredientAttribute->integer_value)
                    ->first();
                if ($activeIngredientOption) {
                    $attributes['active_ingredient']['value'] = $activeIngredientOption->admin_name;
                }
            }
        }

        // Lấy thông tin unit type từ attribute options nếu là select
        if (isset($attributes['unit_type'])) {
            $unitTypeAttribute = $product->attribute_values->where('attribute.code', 'unit_type')->first();
            if ($unitTypeAttribute && $unitTypeAttribute->attribute->type === 'select') {
                $unitTypeOption = \DB::table('attribute_options')
                    ->where('id', $unitTypeAttribute->integer_value)
                    ->first();
                if ($unitTypeOption) {
                    $attributes['unit_type']['value'] = $unitTypeOption->admin_name;
                }
            }
        }

        return $attributes;
    }

    /**
     * Lấy sản phẩm cùng thương hiệu
     */
    private function getRelatedProductsByBrand($currentProduct)
    {
        // Lấy brand của sản phẩm hiện tại
        $brandAttribute = $currentProduct->attribute_values->where('attribute.code', 'brand')->first();

        if (!$brandAttribute || !$brandAttribute->integer_value) {
            return collect();
        }

        // Lấy các sản phẩm cùng brand (trừ sản phẩm hiện tại)
        $relatedProducts = Product::with(['attribute_values.attribute', 'images', 'categories.translation'])
            ->whereHas('attribute_values', function($query) use ($brandAttribute) {
                $query->where('attribute_id', $brandAttribute->attribute_id)
                      ->where('integer_value', $brandAttribute->integer_value);
            })
            ->whereHas('product_flats', function($query) {
                $query->where('status', 1); // Chỉ lấy sản phẩm active từ product_flat
            })
            ->where('id', '!=', $currentProduct->id)
            ->limit(5) // Giới hạn 5 sản phẩm
            ->get();

        // Xử lý dữ liệu sản phẩm
        if ($relatedProducts->count() > 0) {
            $relatedProducts = $this->productController->processProductData($relatedProducts);
        }

        return $relatedProducts;
    }

    /**
     * Lấy giá trị của attribute
     */
    private function getAttributeValue($attributeValue)
    {
        $attribute = $attributeValue->attribute;

        switch ($attribute->type) {
            case 'text':
            case 'textarea':
                return $attributeValue->text_value;
            case 'select':
                return $attributeValue->integer_value;
            case 'boolean':
                return $attributeValue->boolean_value;
            case 'price':
                return $attributeValue->float_value;
            case 'date':
                return $attributeValue->date_value;
            case 'datetime':
                return $attributeValue->datetime_value;
            default:
                return $attributeValue->text_value;
        }
    }
}
